{"logs": [{"outputFile": "com.example.zuijiji.app-mergeDebugResources-47:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b73d418a0e7dd7a4ef8534124f1e5ff4\\transformed\\material3-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,264,370,474,566,655,761,880,990,1112,1194,1291,1376,1466,1575,1689,1791,1904,2015,2127,2260,2369,2473,2580,2689,2775,2870,2979,3088,3179,3277,3374,3488,3607,3706,3798,3872,3961,4049,4143,4226,4308,4403,4483,4565,4662,4757,4852,4949,5032,5128,5222,5320,5437,5517,5611", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "155,259,365,469,561,650,756,875,985,1107,1189,1286,1371,1461,1570,1684,1786,1899,2010,2122,2255,2364,2468,2575,2684,2770,2865,2974,3083,3174,3272,3369,3483,3602,3701,3793,3867,3956,4044,4138,4221,4303,4398,4478,4560,4657,4752,4847,4944,5027,5123,5217,5315,5432,5512,5606,5697"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1349,1454,1558,1664,1768,1860,1949,2055,2174,2284,2406,2488,2585,2670,2760,2869,2983,3085,3198,3309,3421,3554,3663,3767,3874,3983,4069,4164,4273,4382,4473,4571,4668,4782,4901,5000,5092,5166,5255,5343,5437,5520,5602,5697,5777,5859,5956,6051,6146,6243,6326,6422,6516,6614,6731,6811,6905", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "1449,1553,1659,1763,1855,1944,2050,2169,2279,2401,2483,2580,2665,2755,2864,2978,3080,3193,3304,3416,3549,3658,3762,3869,3978,4064,4159,4268,4377,4468,4566,4663,4777,4896,4995,5087,5161,5250,5338,5432,5515,5597,5692,5772,5854,5951,6046,6141,6238,6321,6417,6511,6609,6726,6806,6900,6991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd8a2b0658321fc8309b5e3adb9e8801\\transformed\\foundation-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,78", "endOffsets": "132,211"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "7944,8026", "endColumns": "81,78", "endOffsets": "8021,8100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7bc1e5d916b5089273277ee0d7bd1bd\\transformed\\core-1.16.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,297,391,488,584,682,7591", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "192,292,386,483,579,677,777,7687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d5ea56d900640d3f85abb8bdb6abc3a8\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,903,979,1047,1123,1197,1267,1341,1405", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,75,73,69,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,898,974,1042,1118,1192,1262,1336,1400,1514"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,861,937,1025,1115,1195,1270,6996,7075,7154,7227,7303,7371,7447,7521,7692,7766,7830", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,75,73,69,73,63,113", "endOffsets": "856,932,1020,1110,1190,1265,1344,7070,7149,7222,7298,7366,7442,7516,7586,7761,7825,7939"}}]}]}