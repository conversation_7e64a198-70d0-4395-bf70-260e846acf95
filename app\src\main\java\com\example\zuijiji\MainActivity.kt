package com.example.zuijiji

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import kotlin.math.roundToInt
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.zuijiji.ui.theme.ZuijijiTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ZuijijiTheme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var showBlurDialog by remember { mutableStateOf(false) }
    var dialogOffset by remember { mutableStateOf(Offset.Zero) }

    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景内容层
        BackgroundLayer(
            backgroundImageUri = backgroundImageUri,
            imagePickerLauncher = imagePickerLauncher,
            onShowDialog = { showBlurDialog = true },
            dialogOffset = dialogOffset,
            showDialog = showBlurDialog
        )

        // 毛玻璃弹窗
        if (showBlurDialog) {
            RealTimeBlurDialog(
                backgroundImageUri = backgroundImageUri,
                onDismiss = { showBlurDialog = false },
                onOffsetChange = { dialogOffset = it }
            )
        }
    }
}

@Composable
fun BackgroundLayer(
    backgroundImageUri: Uri?,
    imagePickerLauncher: androidx.activity.result.ActivityResultLauncher<String>,
    onShowDialog: () -> Unit,
    dialogOffset: Offset,
    showDialog: Boolean
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "背景图片",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }

        // 控制按钮
        Column(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Button(
                onClick = { imagePickerLauncher.launch("image/*") },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("导入背景图片")
            }

            Button(
                onClick = onShowDialog,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("打开毛玻璃弹窗")
            }
        }
    }
}

@Composable
fun RealTimeBlurDialog(
    backgroundImageUri: Uri?,
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(Offset.Zero) }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.1f))
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 毛玻璃效果的正方形弹窗
        Box(
            modifier = Modifier
                .size(300.dp) // 固定正方形大小
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        ) {
            // 实时背景模糊层 - 重新渲染背景内容
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(20.dp))
            ) {
                // 背景图片 - 保持全屏尺寸，根据弹窗位置进行偏移
                backgroundImageUri?.let { uri ->
                    Image(
                        painter = rememberAsyncImagePainter(
                            ImageRequest.Builder(LocalContext.current)
                                .data(uri)
                                .build()
                        ),
                        contentDescription = "实时模糊背景",
                        modifier = Modifier
                            .layout { measurable, constraints ->
                                // 强制使用屏幕尺寸而不是弹窗尺寸
                                val screenWidth = constraints.maxWidth * 4 // 假设弹窗是屏幕的1/4宽度
                                val screenHeight = constraints.maxHeight * 4 // 假设弹窗是屏幕的1/4高度
                                val placeable = measurable.measure(
                                    constraints.copy(
                                        minWidth = screenWidth,
                                        maxWidth = screenWidth,
                                        minHeight = screenHeight,
                                        maxHeight = screenHeight
                                    )
                                )
                                layout(constraints.maxWidth, constraints.maxHeight) {
                                    placeable.place(
                                        -offset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2,
                                        -offset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2
                                    )
                                }
                            }
                            .blur(100.dp), // 模糊效果 - 增加到3倍
                        contentScale = ContentScale.Crop
                    )
                }

                // 控制按钮的模糊版本（如果在弹窗区域内）
                Box(
                    modifier = Modifier
                        .layout { measurable, constraints ->
                            // 强制使用屏幕尺寸
                            val screenWidth = constraints.maxWidth * 4
                            val screenHeight = constraints.maxHeight * 4
                            val placeable = measurable.measure(
                                constraints.copy(
                                    minWidth = screenWidth,
                                    maxWidth = screenWidth,
                                    minHeight = screenHeight,
                                    maxHeight = screenHeight
                                )
                            )
                            layout(constraints.maxWidth, constraints.maxHeight) {
                                placeable.place(
                                    -offset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2,
                                    -offset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2
                                )
                            }
                        }
                        .blur(100.dp) // 模糊效果 - 增加到3倍
                ) {
                    // 重新渲染按钮区域
                    Column(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .width(200.dp)
                                .height(40.dp)
                                .background(
                                    MaterialTheme.colorScheme.primary,
                                    RoundedCornerShape(20.dp)
                                )
                        )

                        Box(
                            modifier = Modifier
                                .width(200.dp)
                                .height(40.dp)
                                .background(
                                    MaterialTheme.colorScheme.primary,
                                    RoundedCornerShape(20.dp)
                                )
                        )
                    }
                }
            }



            // 边框高光


            // 弹窗内容











        }
    }
}



