  Activity android.app  
MainScreen android.app.Activity  ZuijijiTheme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Context android.content  
MainScreen android.content.Context  ZuijijiTheme android.content.Context  enableEdgeToEdge android.content.Context  
setContent android.content.Context  
MainScreen android.content.ContextWrapper  ZuijijiTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
MainScreen  android.view.ContextThemeWrapper  ZuijijiTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  ZuijijiTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
MainScreen -androidx.activity.ComponentActivity.Companion  ZuijijiTheme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
BlurDialog "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  ImageRequest "androidx.compose.foundation.layout  LocalContext "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  ZuijijiTheme "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  blur "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberAsyncImagePainter "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  wrapContentHeight "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  
BlurDialog +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  ImageRequest +androidx.compose.foundation.layout.BoxScope  LocalContext +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  blur +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  
cardElevation +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  rememberAsyncImagePainter +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  wrapContentHeight +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  Color +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  RoundedCornerShape !androidx.compose.foundation.shape  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
BlurDialog androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Image androidx.compose.material3  ImageRequest androidx.compose.material3  LocalContext androidx.compose.material3  
MainScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  ZuijijiTheme androidx.compose.material3  align androidx.compose.material3  blur androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberAsyncImagePainter androidx.compose.material3  setValue androidx.compose.material3  spacedBy androidx.compose.material3  wrapContentHeight androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  
BlurDialog androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  Image androidx.compose.runtime  ImageRequest androidx.compose.runtime  LocalContext androidx.compose.runtime  
MainScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Text androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  ZuijijiTheme androidx.compose.runtime  align androidx.compose.runtime  blur androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberAsyncImagePainter androidx.compose.runtime  setValue androidx.compose.runtime  spacedBy androidx.compose.runtime  wrapContentHeight androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  blur androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  wrapContentHeight androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  blur androidx.compose.ui.draw  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  ZuijijiTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  AsyncImagePainter coil.compose  rememberAsyncImagePainter coil.compose  ImageRequest coil.request  Builder coil.request.ImageRequest  build !coil.request.ImageRequest.Builder  data !coil.request.ImageRequest.Builder  ActivityResultContracts com.example.zuijiji  	Alignment com.example.zuijiji  Arrangement com.example.zuijiji  
BlurDialog com.example.zuijiji  Box com.example.zuijiji  Bundle com.example.zuijiji  Button com.example.zuijiji  ButtonDefaults com.example.zuijiji  Card com.example.zuijiji  CardDefaults com.example.zuijiji  Color com.example.zuijiji  Column com.example.zuijiji  ComponentActivity com.example.zuijiji  
Composable com.example.zuijiji  ContentScale com.example.zuijiji  Image com.example.zuijiji  ImageRequest com.example.zuijiji  LocalContext com.example.zuijiji  MainActivity com.example.zuijiji  
MainScreen com.example.zuijiji  
MaterialTheme com.example.zuijiji  Modifier com.example.zuijiji  RoundedCornerShape com.example.zuijiji  Text com.example.zuijiji  Unit com.example.zuijiji  Uri com.example.zuijiji  ZuijijiTheme com.example.zuijiji  align com.example.zuijiji  blur com.example.zuijiji  buttonColors com.example.zuijiji  
cardColors com.example.zuijiji  
cardElevation com.example.zuijiji  fillMaxSize com.example.zuijiji  fillMaxWidth com.example.zuijiji  getValue com.example.zuijiji  let com.example.zuijiji  mutableStateOf com.example.zuijiji  padding com.example.zuijiji  provideDelegate com.example.zuijiji  remember com.example.zuijiji  rememberAsyncImagePainter com.example.zuijiji  setValue com.example.zuijiji  spacedBy com.example.zuijiji  wrapContentHeight com.example.zuijiji  
MainScreen  com.example.zuijiji.MainActivity  ZuijijiTheme  com.example.zuijiji.MainActivity  enableEdgeToEdge  com.example.zuijiji.MainActivity  
setContent  com.example.zuijiji.MainActivity  Boolean com.example.zuijiji.ui.theme  Build com.example.zuijiji.ui.theme  
Composable com.example.zuijiji.ui.theme  DarkColorScheme com.example.zuijiji.ui.theme  
FontFamily com.example.zuijiji.ui.theme  
FontWeight com.example.zuijiji.ui.theme  LightColorScheme com.example.zuijiji.ui.theme  Pink40 com.example.zuijiji.ui.theme  Pink80 com.example.zuijiji.ui.theme  Purple40 com.example.zuijiji.ui.theme  Purple80 com.example.zuijiji.ui.theme  PurpleGrey40 com.example.zuijiji.ui.theme  PurpleGrey80 com.example.zuijiji.ui.theme  
Typography com.example.zuijiji.ui.theme  Unit com.example.zuijiji.ui.theme  ZuijijiTheme com.example.zuijiji.ui.theme  	Function0 kotlin  	Function1 kotlin  let kotlin  sp 
kotlin.Double  	compareTo 
kotlin.Int  KMutableProperty0 kotlin.reflect  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  zIndex "androidx.compose.foundation.layout  Box +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  zIndex +androidx.compose.foundation.layout.BoxScope  
background androidx.compose.material3  clip androidx.compose.material3  zIndex androidx.compose.material3  
background androidx.compose.runtime  clip androidx.compose.runtime  zIndex androidx.compose.runtime  zIndex androidx.compose.ui  clip androidx.compose.ui.Modifier  zIndex androidx.compose.ui.Modifier  clip androidx.compose.ui.draw  
background com.example.zuijiji  clip com.example.zuijiji  zIndex com.example.zuijiji                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  