{"logs": [{"outputFile": "com.example.zuijiji.app-mergeDebugResources-47:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7bc1e5d916b5089273277ee0d7bd1bd\\transformed\\core-1.16.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,312,414,515,622,727,8552", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "197,307,409,510,617,722,841,8648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d5ea56d900640d3f85abb8bdb6abc3a8\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1017,1105,1177,1254,1332,1408,1491,1560", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,1012,1100,1172,1249,1327,1403,1486,1555,1676"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "846,949,1042,1145,1248,1332,1408,7902,7993,8077,8161,8249,8321,8398,8476,8653,8736,8805", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,76,77,75,82,68,120", "endOffsets": "944,1037,1140,1243,1327,1403,1494,7988,8072,8156,8244,8316,8393,8471,8547,8731,8800,8921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b73d418a0e7dd7a4ef8534124f1e5ff4\\transformed\\material3-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,432,551,648,744,857,987,1108,1255,1339,1438,1534,1630,1743,1872,1976,2119,2262,2407,2595,2735,2862,2992,3126,3223,3320,3457,3592,3695,3800,3905,4050,4200,4308,4411,4498,4590,4685,4798,4895,4985,5094,5174,5257,5357,5459,5555,5653,5741,5848,5948,6052,6171,6251,6361", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "169,290,427,546,643,739,852,982,1103,1250,1334,1433,1529,1625,1738,1867,1971,2114,2257,2402,2590,2730,2857,2987,3121,3218,3315,3452,3587,3690,3795,3900,4045,4195,4303,4406,4493,4585,4680,4793,4890,4980,5089,5169,5252,5352,5454,5550,5648,5736,5843,5943,6047,6166,6246,6356,6453"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1499,1618,1739,1876,1995,2092,2188,2301,2431,2552,2699,2783,2882,2978,3074,3187,3316,3420,3563,3706,3851,4039,4179,4306,4436,4570,4667,4764,4901,5036,5139,5244,5349,5494,5644,5752,5855,5942,6034,6129,6242,6339,6429,6538,6618,6701,6801,6903,6999,7097,7185,7292,7392,7496,7615,7695,7805", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "1613,1734,1871,1990,2087,2183,2296,2426,2547,2694,2778,2877,2973,3069,3182,3311,3415,3558,3701,3846,4034,4174,4301,4431,4565,4662,4759,4896,5031,5134,5239,5344,5489,5639,5747,5850,5937,6029,6124,6237,6334,6424,6533,6613,6696,6796,6898,6994,7092,7180,7287,7387,7491,7610,7690,7800,7897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd8a2b0658321fc8309b5e3adb9e8801\\transformed\\foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8926,9014", "endColumns": "87,94", "endOffsets": "9009,9104"}}]}]}